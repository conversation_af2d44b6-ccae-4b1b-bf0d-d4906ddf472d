{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\Documents\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}], "android": [{"name": "integration_test", "path": "C:\\\\Users\\\\<USER>\\\\Documents\\\\flutter\\\\packages\\\\integration_test\\\\", "native_build": true, "dependencies": [], "dev_dependency": true}], "macos": [], "linux": [], "windows": [], "web": [{"name": "drag_and_drop_flutter_web", "path": "C:\\\\Users\\\\<USER>\\\\Documents\\\\flutter_packages\\\\drag_and_drop_flutter_web-0.2.1\\\\", "dependencies": [], "dev_dependency": true}]}, "dependencyGraph": [{"name": "drag_and_drop_flutter_web", "dependencies": []}, {"name": "integration_test", "dependencies": []}], "date_created": "2025-06-02 23:19:31.199235", "version": "3.32.1", "swift_package_manager_enabled": {"ios": false, "macos": false}}