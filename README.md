# drag_and_drop_flutter_web

The web implementation of [`drag_and_drop_flutter`][1].

## Usage

This package is [endorsed][2], which means you can simply use `drag_and_drop_flutter`
normally. This package will be automatically included in your app when you do.

[1]: https://pub.dev/packages/drag_and_drop_flutter
[2]: https://flutter.dev/docs/development/packages-and-plugins/developing-packages#endorsed-federated-plugin
